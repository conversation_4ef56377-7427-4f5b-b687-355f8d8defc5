import Mathlib.Data.Nat.Basic
import Mathlib.Algebra.Order.Ring.Basic
import Mathlib.Data.Finite.Defs
import Mathlib.Data.Nat.Set
import Mathlib.Data.Set.Finite.Basic

open Nat

-- AMC 12A 2002 Problem 6: Determine how many positive integers m admit
-- at least one positive integer n such that mn ≤ m + n

-- Main theorem: All positive integers m work
theorem amc12a_2002_p6 :
  ∀ m : ℕ, m > 0 → ∃ n : ℕ, n > 0 ∧ m * n ≤ m + n := by
  intro m hm
  -- Use n = 1 as the witness
  use 1
  constructor
  · -- Show 1 > 0
    exact Nat.one_pos
  · -- Show m * 1 ≤ m + 1
    rw [mul_one]
    exact Nat.le_add_right m 1

-- Corollary: There are infinitely many such positive integers m
theorem infinitely_many_m :
  Set.Infinite {m : ℕ | m > 0 ∧ ∃ n : ℕ, n > 0 ∧ m * n ≤ m + n} := by
  -- Since all positive integers work, the set equals ℕ+
  have h_eq : {m : ℕ | m > 0 ∧ ∃ n : ℕ, n > 0 ∧ m * n ≤ m + n} = {m : ℕ | m > 0} := by
    ext m
    constructor
    · intro h
      exact h.1
    · intro hm
      constructor
      · exact hm
      · exact amc12a_2002_p6 m hm
  rw [h_eq]
  -- Show {m : ℕ | m > 0} = range Nat.succ and use infinite range
  have h_eq_range : {m : ℕ | m > 0} = Set.range Nat.succ := by
    rw [← Nat.range_succ]
  rw [h_eq_range]
  exact Set.infinite_range_of_injective Nat.succ_injective

-- Alternative proof using algebraic manipulation
theorem amc12a_2002_p6_alt :
  ∀ m : ℕ, m > 0 → ∃ n : ℕ, n > 0 ∧ m * n ≤ m + n := by
  intro m hm
  -- Rewrite mn ≤ m + n as (m-1)(n-1) ≤ 1
  have h_equiv : ∀ (a b : ℕ), a > 0 → b > 0 →
    (a * b ≤ a + b ↔ (a - 1) * (b - 1) ≤ 1) := by
    intro a b ha hb
    -- Case analysis on b
    cases' b with b
    · contradiction -- b > 0 but b = 0
    cases' b with b
    · -- Case b = 1: (a-1)*0 = 0 ≤ 1, and a*1 = a ≤ a + 1
      simp [Nat.mul_zero, Nat.mul_one]
    cases' b with b
    · -- Case b = 2: (a-1)*1 = a-1 ≤ 1 iff a ≤ 2, and a*2 = 2a ≤ a + 2 iff a ≤ 2
      simp [Nat.mul_one]
      constructor
      · intro h
        -- If 2a ≤ a + 2, then a ≤ 2, so a-1 ≤ 1
        have : a ≤ 2 := by omega
        omega
      · intro h
        -- If a-1 ≤ 1, then a ≤ 2, so 2a ≤ a + 2
        have : a ≤ 2 := by omega
        omega
    · -- Case b ≥ 3: (a-1)*(b-1) ≥ (a-1)*2, need a = 1 for ≤ 1
      have hb_ge : b + 3 ≥ 3 := by omega
      constructor
      · intro h
        -- If a*(b+3) ≤ a + (b+3), then a ≤ 2 (since b ≥ 0)
        have : a * (b + 3) ≤ a + (b + 3) := h
        have : a ≤ 2 := by
          by_contra h_not
          have : a ≥ 3 := by omega
          have : a * (b + 3) ≥ 3 * 3 := by
            apply Nat.mul_le_mul'
            · omega
            · omega
          have : a + (b + 3) ≤ a + (b + 3) := le_refl _
          omega
        -- Now show (a-1)*(b+2) ≤ 1
        cases' a with a
        · contradiction
        cases' a with a
        · simp [Nat.zero_mul]
        cases' a with a
        · simp [Nat.one_mul]
        · -- a ≥ 3, contradiction with a ≤ 2
          omega
      · intro h
        -- If (a-1)*(b+2) ≤ 1, then a = 1 (since b+2 ≥ 2)
        have : (a - 1) * (b + 2) ≤ 1 := h
        have : a = 1 := by
          cases' a with a
          · contradiction
          cases' a with a
          · rfl
          · -- a ≥ 2, so (a-1) ≥ 1 and (b+2) ≥ 2, so (a-1)*(b+2) ≥ 2
            have : (a + 1 - 1) * (b + 2) ≥ 1 * 2 := by
              apply Nat.mul_le_mul'
              · omega
              · omega
            simp at this
            omega
        -- Now show a*(b+3) ≤ a + (b+3) when a = 1
        rw [this]
        simp

  use 1
  constructor
  · -- Show 1 > 0
    exact Nat.one_pos
  · -- Use the equivalence with n = 1
    rw [h_equiv m 1 hm Nat.one_pos]
    -- Show (m-1) * (1-1) = (m-1) * 0 = 0 ≤ 1
    simp [Nat.mul_zero]
