# AMC 12A 2013 Problem 4 - Proof Tree

## Problem Statement
Evaluate $\frac{2^{2014}+2^{2012}}{2^{2014}-2^{2012}}$

## Proof Tree Structure

### ROOT_001 [ROOT]
**Goal**: Prove that $\frac{2^{2014}+2^{2012}}{2^{2014}-2^{2012}} = \frac{5}{3}$
**Parent Node**: None
**Strategy**: Factor out common power and simplify

### STRATEGY_001 [STRATEGY]
**Goal**: Use factorization approach to simplify the expression
**Parent Node**: ROOT_001
**Detailed Plan**:
1. Factor out $2^{2012}$ from both numerator and denominator
2. Simplify the resulting expression with smaller exponents
3. Compute the final rational number

### SUBGOAL_001 [PROVEN]
**Goal**: Factor out $2^{2012}$ from numerator and denominator
**Parent Node**: STRATEGY_001
**Strategy**: Use the identity $2^{2014} = 2^{2012} \cdot 2^2$ to rewrite both terms
**Detailed Plan**:
- Use `pow_add` lemma: $2^{2014} = 2^{2012+2} = 2^{2012} \cdot 2^2$
- Apply `ring` tactic to factor: $2^{2012}(2^2 + 1)$ and $2^{2012}(2^2 - 1)$
**Proof Completion**: Successfully proven using `pow_add` and `ring` tactics
**Mathlib Tactics**: `pow_add`, `ring`

### SUBGOAL_002 [PROMISING]
**Goal**: Simplify the fraction after factorization
**Parent Node**: STRATEGY_001
**Strategy**: Cancel common factors and compute the result
**Detailed Plan**:
- Use h1 and h2 to rewrite the fraction
- Apply division properties to cancel $2^{2012}$
- Use `norm_num` to compute $2^2 = 4$, then $(4+1)/(4-1) = 5/3$
**Mathlib Tactics**: `rw`, `div_mul_cancel`, `norm_num`

### SUBGOAL_003 [TO_EXPLORE]
**Goal**: Formalize the proof in Lean 4 with proper theorem statement
**Parent Node**: STRATEGY_001
**Strategy**: Create a theorem with explicit computation steps
**Detailed Plan**:
- Define theorem with the exact expression
- Use Lean 4 arithmetic simplification tactics
- Verify the computation equals 5/3
