-- AMC 12A 2013 Problem 4: Evaluate (2^{2014}+2^{2012}) / (2^{2014}-2^{2012})
-- Solution approach: Factor out 2^{2012} and simplify

import Mathlib.Algebra.Group.Defs
import Mathlib.Tactic.Ring

theorem amc12a_2013_p4 : (2^2014 + 2^2012) / (2^2014 - 2^2012) = 5 / 3 := by
  -- Step 1: Factor out 2^2012 from numerator and denominator
  have h1 : 2^2014 + 2^2012 = 2^2012 * (2^2 + 1) := by
    rw [show 2^2014 = 2^(2012 + 2) from rfl, pow_add]
    ring
  have h2 : 2^2014 - 2^2012 = 2^2012 * (2^2 - 1) := by
    rw [show 2^2014 = 2^(2012 + 2) from rfl, pow_add]
    ring

  -- Step 2: Rewrite the fraction using factorization
  have h3 : (2^2014 + 2^2012) / (2^2014 - 2^2012) = (2^2012 * (2^2 + 1)) / (2^2012 * (2^2 - 1)) := by
    rw [h1, h2]

  -- Step 3: Cancel common factor 2^2012
  have h4 : (2^2012 * (2^2 + 1)) / (2^2012 * (2^2 - 1)) = (2^2 + 1) / (2^2 - 1) := by
    have h_ne_zero : (2 : ℚ)^2012 ≠ 0 := by norm_num
    rw [mul_div_mul_left _ _ h_ne_zero]

  -- Step 4: Compute 2^2 = 4
  have h5 : (2^2 + 1) / (2^2 - 1) = (4 + 1) / (4 - 1) := by norm_num

  -- Step 5: Simplify arithmetic
  have h6 : (4 + 1) / (4 - 1) = 5 / 3 := by norm_num

  -- Combine all steps
  rw [h3, h4, h5, h6]
